/* Base styles and resets */

html {
  font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica,
    Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Updated color scheme */
:root {
  --primary-bg: #0f0f23;
  --secondary-bg: #1a1a2e;
  --accent-color: #00d4ff;
  --text-color: #ffffff;
  --card-bg: #1e1e42;
  --header-bg: #0a0a1a;
}

body {
  background: var(--primary-bg);
  min-height: 100vh;
  font-family: monospace; /* "Arial", sans-serif */
  overflow: hidden;
  color: var(--text-color);
}

/* Landing Page Styles */
.landing-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100vh;
  background: radial-gradient(circle at center, #1a1a2e, #16213e, #0f0f23);
  text-align: center;
  position: relative;
  animation: fadeIn 1.5s ease forwards;
}

.landing-content {
  animation: fadeUp 1.5s ease forwards;
}

.landing-title {
  font-size: 2.5rem;
  color: #00d4ff;
  margin-bottom: 10px;
  opacity: 0;
  animation: fadeUp 1.2s ease forwards;
}

.landing-subtitle {
  font-size: 1.2rem;
  color: #ccc;
  opacity: 0;
  animation: fadeUp 1.2s ease forwards;
  animation-delay: 0.3s;
}

.start-chat-btn {
  margin-top: 30px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: bold;
  background-color: #00d4ff;
  color: #000;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  transition: all 0.3s ease;
}

.start-chat-btn:hover {
  background-color: #00a8cc;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
  transform: scale(1.05);
}

.landing-footer {
  position: absolute;
  bottom: 10px;
  margin-bottom: 50px;
  width: 100%;
  color: #00d4ff; /* Adjust color for visibility against the background */
  font-size: 1.2em;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.footerLogo{
  height: 40px; /* Adjust the height of the logo */
  width: auto; /* Maintain aspect ratio */
  /* The 'align-items: center' on the parent handles vertical alignment */
}

/* Main Layout - Updated */
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: var(--primary-bg);
}

/* Header styles */
.app-header {
  background-color: var(--header-bg);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  height: 60px;
  position: relative;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-color);
  display: flex; /* Make it a flex container */
  align-items: center; /* Vertically align items */
}

.jmsclogo {
  height: 30px; /* Adjust as needed */
  width: auto; /* Maintain aspect ratio */
  margin-right: 10px; /* Space between logo and text */
  vertical-align: middle; /* Align with text */
}

.highlight {
  color: var(--accent-color);
}

/* Fullscreen Button */
.fullscreen-btn {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
}

.fullscreen-btn:hover {
  background-color: rgba(0, 212, 255, 0.1);
  color: var(--accent-color);
  transform: scale(1.05);
}

.fullscreen-btn:active {
  transform: scale(0.95);
}

.fullscreen-btn i {
  transition: transform 0.2s ease;
}

.fullscreen-btn:hover i {
  transform: scale(1.1);
}

/* Content container */
.content-container {
  display: flex;
  flex-direction: row; /* Default for larger screens: left panel on left, right panel on right */
  height: calc(100vh - 60px); /* Adjust for header */
  width: 100%;
}

/* Left panel - Updated */
.left-panel {
  width: 100%; /* Default width for left panel */
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
  border-right: none;
  transition: width 0.5s ease, border-right 0.5s ease;
  order: 0; /* Default order */
}

/* Left panel when products are shown - Increased from 30% to 40% */
.left-panel.split-view {
  width: 30%; /* Default split view width */
  border-right: 1px solid rgba(0, 212, 255, 0.2);
}

/* Visualizer container - Updated */
.visualizer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.main-circle {
  position: relative;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: radial-gradient(circle, #0f0f23 60%, #1a1a2e);
  border: 3px solid #00d4ff;
  box-shadow: 0 0 30px #00d4ff, 0 0 60px rgba(0, 212, 255, 0.5),
    0 0 90px rgba(0, 212, 255, 0.3), inset 0 0 30px rgba(0, 212, 255, 0.1);
  animation: pulse-glow 2s ease-in-out infinite alternate;
  display: flex;
  justify-content: center;
  align-items: center;
}

.audio-bars {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 40px;
}

.bar {
  width: 6px;
  background: linear-gradient(to top, #00d4ff, #ffffff);
  border-radius: 3px;
  animation: audio-dance 1.5s ease-in-out infinite;
  box-shadow: 0 0 10px #00d4ff;
}

.bar:nth-child(1) {
  height: 20px;
  animation-delay: 0s;
}
.bar:nth-child(2) {
  height: 35px;
  animation-delay: 0.1s;
}
.bar:nth-child(3) {
  height: 15px;
  animation-delay: 0.2s;
}
.bar:nth-child(4) {
  height: 30px;
  animation-delay: 0.3s;
}
.bar:nth-child(5) {
  height: 25px;
  animation-delay: 0.4s;
}

.outer-ring {
  position: absolute;
  width: 300px;
  height: 300px;
  border: 2px solid rgba(0, 212, 255, 0.3);
  border-radius: 50%;
  top: -25px;
  left: -25px;
  animation: rotate 10s linear infinite;
}

.outer-ring::before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background: #00d4ff;
  border-radius: 50%;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 15px #00d4ff;
}

/* Listening indicator - New */
.listening-indicator {
  margin-top: 15px;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
}

.listening-icon {
  width: 12px;
  height: 12px;
  background-color: var(--accent-color);
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

/* Message container - Better proportional sizing */
  .message-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    padding: 15px;
    width: 85%;
    max-width: 600px;
    min-width: 300px;
    max-height: calc(100% - 320px);
    overflow: hidden;
    gap: 15px;
    box-sizing: border-box;
  }

  #messages {
    flex: 1; /* Allow messages to take available space and push input to bottom */
    padding-bottom: 90px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 0;
    overflow-y: auto; /* This is where the scrolling should happen */
  }

  #messageForm {
    padding: 5px 8px;
    min-height: 40px;
    margin-top: auto; /* Push the form to the bottom of the container */
  }

/* Updated message form - Text area style */
#messageForm {
  display: flex;
  align-items: flex-end;
  border-radius: 16px;
  padding: 8px 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  background-color: rgba(30, 30, 66, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  min-height: 50px;
  max-width: 100%;
  box-sizing: border-box;
}

#messageForm:focus-within {
  border-color: var(--accent-color);
  box-shadow: 0 4px 25px rgba(0, 212, 255, 0.4);
  transform: translateY(-2px);
}

#message {
  flex: 1;
  padding: 12px 16px;
  border-radius: 12px;
  border: none;
  background-color: transparent;
  color: var(--text-color);
  font-size: 15px;
  line-height: 1.4;
  resize: none;
  min-height: 24px;
  max-height: 120px;
  overflow: hidden;
  font-family: inherit;
  word-wrap: break-word;
  box-sizing: border-box;
  width: 100%;
}

#message:focus {
  outline: none;
}

#message::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.mic-button {
  background-color: transparent;
  color: var(--accent-color);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  margin-right: 5px;
}

.mic-button:hover {
  background-color: rgba(0, 212, 255, 0.1);
}

.mic-button.active {
  background-color: var(--accent-color);
  color: #000;
}

#sendButton {
  background-color: var(--accent-color);
  color: #000;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

#sendButton:hover {
  background-color: #00a8cc;
  transform: scale(1.05);
}

/* Right panel - Updated */
.right-panel {
  width: 0%;
  padding: 0;
  background-color: rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  opacity: 0;
  transition: width 0.5s ease, padding 0.5s ease, opacity 0.5s ease;
}

/* Right panel when products are shown - Decreased from 70% to 60% */
.right-panel.show {
  width: 70%;
  padding: 20px;
  opacity: 1;
}

.recommendations-panel {
  color: #eee;
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recommendations-panel h2 {
  margin-bottom: 20px;
  color: #eee;
  font-size: 1.5rem;
  font-weight: normal;
}

/* Product grid - Modern responsive layout */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 16px;
}

/* Ensure products display in grid format */
#recommendedProducts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  padding: 16px;
}

/* Recipe container styles */
.recipe-container {
  grid-column: 1 / -1; /* Span full width of the grid */
  background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.9));
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 24px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.recipe-header {
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.1),
    rgba(0, 150, 200, 0.1)
  );
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.recipe-title {
  color: var(--text-color);
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.recipe-title::before {
  content: "🍹";
  font-size: 1.6rem;
}

.recipe-content {
  padding: 24px;
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.recipe-content div {
  margin-bottom: 12px;
}

.recipe-content div:last-child {
  margin-bottom: 0;
}

/* Additional recipe styles for formatted messages */
.recipe-section {
  margin-bottom: 20px;
}

.recipe-section-title {
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: bold;
}

.recipe-list {
  margin-left: 20px;
  margin-bottom: 10px;
}

.recipe-list li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: var(--text-color);
}

.recipe-instructions li,
.recipe-steps li {
  margin-bottom: 12px;
  padding-left: 5px;
}

.recipe-text {
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 10px;
}

/* Recipe card panel styles for right panel display */
.recipe-card-panel {
  background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.9));
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 24px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  width: 100%;
}

.recipe-card-header {
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.1),
    rgba(0, 150, 200, 0.1)
  );
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-card-title {
  color: var(--text-color);
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0;
}

.recipe-card-icon {
  font-size: 1.6rem;
}

.recipe-card-content {
  padding: 24px;
  color: var(--text-color);
  line-height: 1.6;
  font-size: 1rem;
}

.recipe-card-content .recipe-section {
  margin-bottom: 20px;
}

.recipe-card-content .recipe-section:last-child {
  margin-bottom: 0;
}

.recipe-card-content h4 {
  color: var(--accent-color);
  font-size: 1.1rem;
  margin-bottom: 10px;
  font-weight: bold;
}

.recipe-card-content ul,
.recipe-card-content ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.recipe-card-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.recipe-card-content p {
  margin-bottom: 10px;
  line-height: 1.6;
}

/* Modern product card with horizontal layout */
.product-card {
  background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.8));
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: row;
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-height: 180px;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.15),
    0 0 0 1px rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.3);
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--accent-color),
    #00a8cc,
    var(--accent-color)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover::before {
  opacity: 1;
}

.product-image-container {
  width: 40%;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.recipe-card-image-container {
  width: 40%;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.product-image-container::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), transparent);
  pointer-events: none;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.recipe-card-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-location {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  white-space: nowrap;
  max-width: calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-details {
  width: 60%;
  flex-shrink: 0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
  background: rgba(255, 255, 255, 0.02);
  position: relative;
}

.product-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.product-category-label {
  background: linear-gradient(
    135deg,
    var(--category-label-color, rgba(255, 255, 255, 0.15)),
    rgba(255, 255, 255, 0.05)
  );
  /* color: #fff; */
  color: var(--category-text-color, #ffffff);
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.product-category-label::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.product-card:hover .product-category-label::before {
  left: 100%;
}

.product-size-handle {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
  color: #ccc;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.product-title {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.4;
  margin: 0 0 8px 0;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.product-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
}

.product-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--accent-color);
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  position: relative;
  margin-bottom: 8px;
}

.product-price::before {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), #00a8cc);
  transition: width 0.3s ease;
}

.product-card:hover .product-price::before {
  width: 100%;
}

.product-info-row {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.product-size-info {
  font-size: 0.85rem;
  color: #bbb;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-block;
  width: fit-content;
}

.product-location-info {
  font-size: 0.8rem;
  color: #aaa;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-block;
  width: fit-content;
  margin-top: 2px;
}

.view-details-btn {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.1),
    rgba(0, 212, 255, 0.05)
  );
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 25px;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
  gap: 10px;
  min-width: 120px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.view-details-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 212, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.view-details-btn:hover::before {
  left: 100%;
}

.view-details-btn:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.2),
    rgba(0, 212, 255, 0.1)
  );
  border-color: var(--accent-color);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.4);
}

.view-details-btn:active {
  transform: translateY(0) scale(0.98);
}

.view-details-btn span {
  font-size: 0.85rem;
  color: #ffffff;
  font-weight: 600;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.view-details-btn .icon {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--accent-color), #00a8cc);
  border-radius: 50%;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
  transition: transform 0.3s ease;
}

.view-details-btn:hover .icon {
  transform: rotate(360deg) scale(1.1);
}

/* Disabled state for view details button */
.view-details-btn:disabled,
.view-details-btn.hidden {
  opacity: 0.4;
  cursor: not-allowed;
  background: rgba(100, 100, 100, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
}

.view-details-btn:disabled:hover,
.view-details-btn.hidden:hover {
  background: rgba(100, 100, 100, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
  box-shadow: none;
}

/* Loading state animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.product-card.loading {
  pointer-events: none;
}

.product-card.loading .product-image {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced no-image placeholder */
.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.02)
  );
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #888;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.no-image::before {
  content: "📷";
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
  opacity: 0.5;
}

.view-details-btn .icon::after {
  content: "→";
}

/* Chat Message Styles - Keep existing with minor updates */
.message {
  font: message-box;
  font-family: monospace;
  padding: 8px 12px;
  border-radius: 15px;
  margin-bottom: 10px;
  max-width: 80%;
  word-wrap: break-word;
  animation: fadeIn 0.3s ease;
  position: relative;
  line-height: 1.4;
  /* background: rgba(255, 255, 255, 0.1); */
  /* backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2); */
}

.user-message {
  background-color: #00d4ff;
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-left: auto;
  margin-right: 20px;
}

.agent-message {
  background-color: #343a40;
  color: white;
  align-self: flex-start;
  border-bottom-left-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-right: auto;
}

.system-message {
  background-color: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  text-align: center;
  font-style: italic;
  margin-left: auto;
  margin-right: auto;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9em;
}

.searching-message {
  background-color: rgba(0, 212, 255, 0.1);
  color: #00d4ff;
  text-align: center;
  font-style: italic;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 1em;
  border: 1px solid rgba(0, 212, 255, 0.3);
  animation: pulse-searching 1.5s ease-in-out infinite;
}

@keyframes pulse-searching {
  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Product Loading States */
.product-card-loading {
  background-color: var(--card-bg);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  animation: pulse-loading 1.5s ease-in-out infinite;
}

.product-card-loading .loading-image {
  width: 100%;
  height: 140px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.product-card-loading .loading-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-card-loading .loading-category {
  width: 80px;
  height: 16px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.product-card-loading .loading-title {
  width: 100%;
  height: 20px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.product-card-loading .loading-price {
  width: 60px;
  height: 18px;
  background: linear-gradient(
    90deg,
    rgba(0, 212, 255, 0.1) 25%,
    rgba(0, 212, 255, 0.2) 50%,
    rgba(0, 212, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.product-card-loading .loading-button {
  width: 100px;
  height: 32px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
  margin-top: 5px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-loading {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Animations */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 30px #00d4ff, 0 0 60px rgba(0, 212, 255, 0.5),
      0 0 90px rgba(0, 212, 255, 0.3), inset 0 0 30px rgba(0, 212, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 40px #00d4ff, 0 0 80px rgba(0, 212, 255, 0.7),
      0 0 120px rgba(0, 212, 255, 0.5), inset 0 0 40px rgba(0, 212, 255, 0.2);
  }
}

@keyframes audio-dance {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.5);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
}

@keyframes fadeUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */

/* Small mobile devices (max-width: 480px) */
@media (max-width: 480px) {
  .jmsclogo {
    height: 25px; /* Smaller logo for small screens */
    margin-right: 8px;
  }

  /* Landing page - portrait */
  .landing-title {
    font-size: 2rem;
    margin-bottom: 8px;
  }

  .landing-subtitle {
    font-size: 1rem;
  }

  .start-chat-btn {
    margin-top: 25px;
    padding: 12px 20px;
    font-size: 0.95rem;
  }

  .landing-footer {
    bottom: 30px;
    font-size: 1rem;
  }

  .footerLogo {
    height: 32px;
  }
}

/* Small mobile landscape */
@media (max-width: 480px) and (orientation: landscape) {
  .landing-container {
    padding: 10px 15px;
  }

  .landing-title {
    font-size: 1.6rem;
    margin-bottom: 4px;
  }

  .landing-subtitle {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  .start-chat-btn {
    margin-top: 15px;
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .landing-footer {
    bottom: 15px;
    font-size: 0.8rem;
  }

  .footerLogo {
    height: 24px;
  }
}

/* Medium mobile and small tablets (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  /* Portrait mode */
  .landing-title {
    font-size: 2.2rem;
  }

  .landing-subtitle {
    font-size: 1.1rem;
  }

  .landing-footer {
    bottom: 40px;
    font-size: 1.1rem;
  }

  .footerLogo {
    height: 36px;
  }
}

/* Medium mobile and small tablets (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .jmsclogo {
    height: 28px; /* Slightly larger logo for medium screens */
    margin-right: 9px;
  }
}

/* Medium mobile landscape */
@media (min-width: 481px) and (max-width: 768px) and (orientation: landscape) {
  .landing-container {
    padding: 15px 20px;
  }

  .landing-title {
    font-size: 1.8rem;
    margin-bottom: 6px;
  }

  .landing-subtitle {
    font-size: 0.95rem;
    margin-bottom: 15px;
  }

  .start-chat-btn {
    margin-top: 20px;
    padding: 10px 18px;
    font-size: 0.9rem;
  }

  .landing-footer {
    bottom: 20px;
    font-size: 0.9rem;
  }

  .footerLogo {
    height: 28px;
  }
}

/* All mobile devices (max-width: 768px) - Main app styles */
@media (max-width: 768px) {
  .jmsclogo {
    height: 25px; /* Consistent smaller logo for all mobile */
    margin-right: 8px;
  }

  .app-header {
    padding: 10px 15px;
  }

  .fullscreen-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .message-container {
    width: 95%;
    min-width: 280px;
    max-width: none;
    padding: 10px;
    max-height: calc(100% - 250px);
  }

  #messageForm {
    padding: 6px 10px;
    border-radius: 12px;
    min-height: 45px;
  }

  #message {
    font-size: 16px;
    padding: 10px 12px;
    max-height: 100px;
  }

  /* Touch-friendly improvements */
  .main-image-container::before {
    content: "← Swipe →";
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: rgba(255, 255, 255, 0.8);
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.6rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
  }

  .main-image-container:active::before {
    opacity: 1;
  }

  .thumbnail-image {
    width: 60px;
    height: 60px;
  }

  .thumbnails-container {
    height: 70px;
    gap: 8px;
  }

  /* Modal optimizations */
  .modal-content {
    max-width: 98vw;
    width: 98vw;
    height: 98vh;
    max-height: 98vh;
    flex-direction: column;
  }

  .modal-body {
    flex-direction: column;
    gap: 12px;
    padding: 12px 15px;
    overflow-y: auto;
  }

  .modal-left-section {
    flex: none;
    height: auto;
    min-height: 300px;
    max-height: 50vh;
    gap: 6px;
  }

  .modal-right-section {
    flex: 1;
    gap: 8px;
    padding-left: 0;
    overflow-y: auto;
  }

  .product-image-gallery {
    height: 85%;
    min-height: 200px;
  }

  .main-image-container {
    padding: 6px;
    margin-bottom: 4px;
  }

  .pairing-recommendations {
    height: 15%;
    min-height: 60px;
    padding: 6px;
  }

  .pairing-title {
    font-size: 0.8rem;
    margin-bottom: 3px;
  }

  .pairing-item {
    font-size: 0.7rem;
    gap: 4px;
  }

  .image-thumbnails {
    height: 64px;
  }

  .thumbnails-container {
    height: 48px;
  }

  .thumbnail-image {
    width: 48px;
    height: 48px;
  }

  .thumbnail-nav {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}


/* Tablet Portrait - 768px to 1024px */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .visualizer-container {
    margin-top: 10px;
  }

  .main-circle {
    width: 180px;
    height: 180px;
  }

  .outer-ring {
    width: 220px;
    height: 220px;
  }

  .message-container {
    width: 90%;
    max-width: 500px;
    max-height: calc(100% - 280px);
  }

  .left-panel.split-view {
    width: 35%;
  }

  .right-panel.show {
    width: 65%;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

/* Tablet Landscape - 768px+ with landscape orientation */
@media (min-width: 768px) and (orientation: landscape) and (max-height: 1024px) {
  .landing-footer {
    bottom: 20px;
    font-size: 0.9rem;
  }

  .footerLogo {
    height: 28px;
  }

  .visualizer-container {
    margin-top: 5px;
  }

  .main-circle {
    width: 160px;
    height: 160px;
  }

  .outer-ring {
    width: 200px;
    height: 200px;
  }

  .message-container {
    width: 60%;
    max-width: 600px;
    max-height: calc(100% - 240px);
  }

  .left-panel.split-view {
    width: 40%;
  }

  .right-panel.show {
    width: 60%;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 18px;
  }
}

/* Large Tablets/Small Laptops */
@media (min-width: 1025px) and (max-width: 1366px) {
  .visualizer-container {
    margin-top: 15px;
  }

  .main-circle {
    width: 220px;
    height: 220px;
  }

  .outer-ring {
    width: 270px;
    height: 270px;
  }

  .message-container {
    width: 98%;
    max-width: 650px;
  }

  #messages {
    flex: 1; /* Allow messages to take available space and push input to bottom */
    padding-bottom: 5px;
    gap: 8px;
    margin-bottom: 0;
    overflow-y: auto; /* This is where the scrolling should happen */
  }

  .product-image-container {
    height: 160px;
    padding: 14px;
  }

  .product-details {
    padding: 18px;
  }

  .product-title {
    font-size: 1.05rem;
  }

  .product-price {
    font-size: 1.3rem;
  }
}

/* Target size: 1024×768 */
@media (min-width: 1024px) and (max-height: 800px) {
  .left-panel.split-view {
    width: 40%;
  }

  .right-panel.show {
    width: 60%;
  }

  .main-circle {
    width: 200px;
    height: 200px;
  }

  .outer-ring {
    width: 250px;
    height: 250px;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }

  .message-container {
    width: 100%;
    max-height: calc(100% - 300px);
  }

  .product-image-container {
    /* height: 120px; */
  }

  .product-location {
    font-size: 0.65rem;
    padding: 3px 6px;
  }
}

/* Target size: 1024×1366 */
@media (min-width: 1024px) and (min-height: 1300px) {
  .left-panel.split-view {
    width: 30%;
  }

  .right-panel.show {
    width: 70%;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(384px, 1fr));
    gap: 22px;
    padding: 18px;
  }

  .product-card {
    border-radius: 20px;
  }

  .product-image-container {
    /* height: 100%; */
    padding: 16px;
  }

  .product-details {
    padding: 20px;
  }

  .product-title {
    font-size: 1.1rem;
  }

  .product-price {
    font-size: 1.4rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-overlay.show {
  display: flex;
}

/* Enhanced Modal Styles - Optimized for space efficiency and responsiveness */
.modal-content {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 0;
  max-width: min(1600px, 97vw);
  width: 97vw;
  max-height: min(96vh, 900px);
  height: 96vh;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
  animation: slideUp 0.3s ease;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  flex-shrink: 0;
}

.modal-title {
  color: var(--accent-color);
  font-size: 1.6rem;
  font-weight: bold;
  margin: 6px 0;
  line-height: 1.2;
}

.close-btn {
  background: none;
  border: none;
  color: #ccc;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--accent-color);
}

.modal-body {
  color: var(--text-color);
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
  display: flex;
  gap: 20px;
  padding: 15px 20px;
  min-height: 0;
}

/* Modal Layout Sections - Responsive and optimized */
.modal-left-section {
  flex: 0 0 clamp(384px, 35%, 500px);
  display: flex;
  flex-direction: column;
  min-height: 0;
  gap: 8px;
  height: 100%;
}

.modal-right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
  overflow-y: auto;
  padding-left: 15px;
  height: 100%;
}

/* Enhanced Product Image Gallery - 85% height allocation */
.product-image-gallery {
  display: flex;
  flex-direction: column;
  height: 85%;
  min-height: 0;
  flex-shrink: 0;
}

.main-image-container {
  text-align: center;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  min-height: 0;
  cursor: grab;
  touch-action: pan-y pinch-zoom;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.main-product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  transition: opacity 0.3s ease, transform 0.2s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.no-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #aaa;
  font-size: 1.1rem;
  text-align: center;
}

/* Pairing Recommendations - 15% height allocation */
.pairing-recommendations {
  /* background: rgba(0, 212, 255, 0.05); */
  /* border: 1px solid rgba(0, 212, 255, 0.15); */
  /* border-radius: 8px; */
  padding: 10px 10px 10px 0px;
  height: 15%;
  flex-shrink: 0;
  /* overflow-y: auto; */
  display: flex;
  flex-direction: column;
}

.pairing-title {
  color: var(--accent-color);
  font-size: 1.2rem;
  /* font-weight: 600; */
  margin: 0 0 6px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pairing-title::before {
  content: "✨";
  font-size: 1.2rem;
}

.pairing-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pairing-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  font-size: 0.8rem;
  line-height: 1.3;
}

.pairing-icon {
  font-size: 0.9rem;
  flex-shrink: 0;
  margin-top: 1px;
}

.pairing-text {
  color: var(--text-color);
  font-size: 0.9rem;
  margin-left: 32px;
}

/* Enhanced Thumbnail Slider - Fixed height */
.image-thumbnails {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  height: 96px;
  flex-shrink: 0;
  margin-top: auto;
}

.thumbnail-nav {
  background: rgba(0, 212, 255, 0.2);
  border: 1px solid var(--accent-color);
  color: var(--accent-color);
  border-radius: 50%;
  width: 35px;
  height: 35px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-nav:hover {
  background: var(--accent-color);
  color: #000;
  transform: scale(1.1);
}

.thumbnails-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  max-width: 100%;
}

.thumbnails-container {
  display: flex;
  gap: 12px;
  /* overflow-x: auto;
  scroll-behavior: smooth; */
  padding: 8px;
  /* scrollbar-width: thin; */
  width: 100%;
  min-width: 0;
  height: 80px;
  align-items: center;
  touch-action: pan-x;
  cursor: grab;
  -webkit-overflow-scrolling: touch;
}

.thumbnail-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  opacity: 0.7;
  flex-shrink: 0;
  touch-action: manipulation;
}

/* Consolidated Mobile Device Styles (384x832) - Single comprehensive block */
@media (max-width: 384px) and (max-height: 832px) {
  /* Logo and Header Adjustments */
  .jmsclogo {
    height: 22px;
    margin-right: 6px;
  }

  /* Landing Page Adjustments */
  .landing-title {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .landing-subtitle {
    font-size: 0.9rem;
  }

  .start-chat-btn {
    margin-top: 20px;
    padding: 10px 18px;
    font-size: 0.9rem;
  }

  .landing-footer {
    bottom: 20px;
    font-size: 0.8rem;
  }

  .footerLogo {
    height: 28px;
  }

  /* Main App Layout - Enhanced viewport handling */
  .app-header {
    padding: 8px 12px;
    height: 50px;
  }

  .logo {
    font-size: 1.3rem;
  }

  .fullscreen-btn {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  /* Use dynamic viewport units for better mobile browser support */
  .content-container {
    height: 100dvh; /* Dynamic viewport height accounts for browser UI */
    min-height: 100svh; /* Small viewport height as fallback */
    flex-direction: column; /* Stack panels vertically */
  }

  /* Left Panel - Chat and Visualizer */
  .left-panel {
    width: 100%;
    height: 60dvh; /* Use dynamic viewport for consistent sizing */
    min-height: 300px; /* Ensure minimum usable space */
    padding: 10px;
    border-right: none;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    justify-content: flex-start;
    order: 2; /* Place chat section second */
    position: relative; /* Enable positioning context */
  }

  .left-panel.split-view {
    width: 100%;
    height: 60dvh;
    min-height: 300px;
    border-right: none;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  }

  /* Message Container - Fixed positioning solution */
  .message-container {
    width: 98%;
    max-width: none;
    padding: 8px;
    height: 100%;
    max-height: none;
    min-height: auto;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  /* Messages Area */
  #messages {
    flex: 1;
    padding-bottom: 60px; /* Space for fixed form */
    gap: 8px;
    margin-bottom: 130px;
    overflow-y: auto;
  }

  /* Message Form - Fixed positioning for keyboard handling */
  #messageForm {
    padding: 5px 8px;
    min-height: 40px;
    /* Fix form to bottom of container */
    position: absolute;
    bottom: 80px;
    left: 8px;
    right: 8px;
    width: calc(100% - 16px);
    /* z-index: 100; */
    /* Ensure form stays above keyboard */
    transform: translateZ(0); /* Force hardware acceleration */
    /* Enhanced visibility */
    background-color: rgba(30, 30, 66, 0.95);
    backdrop-filter: blur(15px);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  }

  /* Keyboard interaction handling */
  .message-container:focus-within #messageForm {
    /* When keyboard is active, ensure form visibility */
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100vw - 20px);
    max-width: 360px; /* Account for 384px - 10px padding */
    z-index: 1000;
    background-color: rgba(30, 30, 66, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 212, 255, 0.3);
  }

  /* Adjust messages area when keyboard is active */
  .message-container:focus-within #messages {
    padding-bottom: 80px; /* More space when keyboard is active */
    max-height: calc(100% - 80px);
  }

  /* Enhanced input styling for mobile */
  #message {
    padding: 8px 10px;
    font-size: 16px; /* Prevent zoom on iOS */
    max-height: 80px;
    box-sizing: border-box;
  }

  /* Button adjustments */
  .mic-button,
  #sendButton {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
    flex-shrink: 0;
  }

  /* Right Panel - Products */
  .right-panel {
    width: 100%;
    height: 40dvh; /* Use dynamic viewport */
    min-height: 200px;
    padding: 0;
    opacity: 0;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    order: 1; /* Place products section first */
    transition: height 0.5s ease, padding 0.5s ease, opacity 0.5s ease;
  }

  .right-panel.show {
    width: 100%;
    height: 40dvh;
    min-height: 200px;
    padding: 10px;
    opacity: 1;
  }

  /* Product Grid Adjustments */
  .recommendations-panel h2 {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    padding: 10px;
  }

  .product-card {
    flex-direction: column;
    min-height: auto;
  }

  .product-image-container {
    width: 100%;
    height: 150px;
    padding: 10px;
  }

  .product-details {
    width: 100%;
    padding: 15px;
    gap: 8px;
  }

  /* Inline Product Cards within Chat */
  .product-card-inline {
    width: 95%;
    max-width: 300px;
    margin: 10px auto;
    flex-direction: column;
    align-self: center;
    background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.9));
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 212, 255, 0.2);
  }

  .product-card-inline .product-image-container {
    width: 100%;
    height: 180px;
    padding: 15px;
  }

  .product-card-inline .product-details {
    width: 100%;
    padding: 15px;
    gap: 10px;
  }

  .product-card-inline .product-title {
    font-size: 1.1rem;
    -webkit-line-clamp: 2;
  }

  .product-card-inline .product-price {
    font-size: 1.3rem;
  }

  .product-card-inline .product-info-row {
    flex-direction: column;
    gap: 4px;
  }

  .product-card-inline .product-footer {
    justify-content: center;
    padding-top: 10px;
  }

  .product-card-loading-inline {
    width: 95%;
    max-width: 300px;
    margin: 10px auto;
    align-self: center;
    border-radius: 16px;
  }

  .product-card-loading-inline .loading-image {
    height: 180px;
  }
}

/* Additional support for very small screens */
@media (max-width: 384px) and (max-height: 700px) {
  .content-container {
    height: 100dvh;
    min-height: 100svh;
  }

  .left-panel,
  .left-panel.split-view {
    height: 65dvh;
    min-height: 280px;
  }

  .right-panel,
  .right-panel.show {
    height: 35dvh;
    min-height: 180px;
  }

  /* More aggressive form positioning for very small screens */
  .message-container:focus-within #messageForm {
    bottom: env(keyboard-inset-height, 0px);
  }
}

/* Fallback for browsers that don't support dvh */
@supports not (height: 100dvh) {
  @media (max-width: 384px) and (max-height: 832px) {
    .content-container {
      height: calc(100vh - 60px);
    }

    .left-panel,
    .left-panel.split-view {
      height: calc(60vh - 36px);
    }

    .right-panel,
    .right-panel.show {
      height: calc(40vh - 24px);
    }
  }
}

/* Touch-friendly improvements for tablets */
@media (min-width: 768px) and (max-width: 1024px) {
  .fullscreen-btn {
    width: 44px;
    height: 44px;
    font-size: 1.3rem;
    min-width: 44px;
    min-height: 44px;
  }

  .mic-button,
  #sendButton {
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
  }

  .view-details-btn {
    padding: 12px 16px;
    min-height: 44px;
    font-size: 0.9rem;
  }

  .selector-option {
    padding: 10px 14px;
    min-height: 44px;
  }

  .thumbnail-nav {
    width: 44px;
    height: 44px;
    font-size: 16px;
  }

  /* Enhanced touch experience for image gallery */
  .main-image-container {
    position: relative;
  }

  .main-image-container::before {
    content: "← Swipe to navigate →";
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: rgba(255, 255, 255, 0.8);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.7rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
  }

  .main-image-container:hover::before {
    opacity: 1;
  }

  /* Larger touch targets for thumbnails */
  .thumbnail-image {
    width: 90px;
    height: 90px;
    margin: 5px;
  }

  .thumbnails-container {
    height: 100px;
    padding: 10px;
  }
}

/* Styles for products displayed inline within the chatbox (384x832) */
@media (max-width: 384px) and (max-height: 832px) {
  .product-card-inline {
    width: 95%; /* Adjust width to fit within chat bubble area */
    max-width: 300px; /* Max width for inline cards */
    margin: 10px auto; /* Center the card and add vertical spacing */
    flex-direction: column; /* Stack image and details vertically */
    align-self: center; /* Center within the messages div */
    background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.9));
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 212, 255, 0.2);
  }

  .product-card-inline .product-image-container {
    width: 100%;
    height: 180px; /* Larger image area for inline cards */
    padding: 15px;
  }

  .product-card-inline .product-details {
    width: 100%;
    padding: 15px;
    gap: 10px;
  }

  .product-card-inline .product-title {
    font-size: 1.1rem;
    -webkit-line-clamp: 2; /* Limit title lines */
  }

  .product-card-inline .product-price {
    font-size: 1.3rem;
  }

  .product-card-inline .product-info-row {
    flex-direction: column; /* Stack info rows vertically for better readability */
    gap: 4px;
  }

  .product-card-inline .product-footer {
    justify-content: center; /* Center the button */
    padding-top: 10px;
  }

  .product-card-loading-inline {
    width: 95%;
    max-width: 300px;
    margin: 10px auto;
    align-self: center;
    border-radius: 16px;
  }

  .product-card-loading-inline .loading-image {
    height: 180px;
  }
}
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.thumbnail-image:hover {
  opacity: 1;
  transform: scale(1.05);
  border-color: rgba(0, 212, 255, 0.5);
}

.thumbnail-image.active {
  opacity: 1;
  border-color: var(--accent-color);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
  transform: scale(1.02);
}

/* Product Info Styles */
.product-info {
  display: flex;
  flex-direction: column;
  /* gap: 20px; */
}
.hidden {
  display: none;
}

.product-category-badge {
  background: linear-gradient(
    135deg,
    var(--category-label-color, rgba(255, 255, 255, 0.15)),
    rgba(255, 255, 255, 0.05)
  );
  /* color: #fff; */
  color: var(--category-text-color, #ffffff);  
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-left: auto;
  margin-right: 75px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-title-large {
  font-size: 1.6rem;
  font-weight: bold;
  color: var(--text-color);
  margin: 6px 0;
  line-height: 1.2;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 6px 0;
  flex-wrap: wrap;
}

.stars-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #ffd700;
  font-size: 1.2rem;
  position: relative;
}

.rating-value {
  color: #aaa;
  font-size: 0.9rem;
  margin-left: 5px;
}

/* Enhanced star rating styles */
.star.full-star {
  color: #ffd700;
}

.star.empty-star {
  color: rgba(255, 215, 0, 0.3);
}

.star.half-star {
  color: #ffd700;
  position: relative;
  overflow: hidden;
}

.star.half-star:after {
  content: "☆";
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  color: rgba(255, 215, 0, 0.3);
  overflow: hidden;
}

.star.quarter-star {
  color: #ffd700;
  position: relative;
  overflow: hidden;
}

.star.quarter-star:after {
  content: "☆";
  position: absolute;
  top: 0;
  left: 25%;
  width: 75%;
  color: rgba(255, 215, 0, 0.3);
  overflow: hidden;
}

.star.three-quarter-star {
  color: #ffd700;
  position: relative;
  overflow: hidden;
}

.star.three-quarter-star:after {
  content: "☆";
  position: absolute;
  top: 0;
  left: 75%;
  width: 25%;
  color: rgba(255, 215, 0, 0.3);
  overflow: hidden;
}

.brand-info {
  color: #aaa;
  font-size: 0.9rem;
}

.product-price-large {
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--accent-color);
  margin: 8px 0;
}

.product-description-text {
  color: #ccc;
  line-height: 1.5;
  margin: 8px 0;
  max-height: 120px;
  overflow-y: auto;
  padding-right: 10px;
  font-size: 0.9rem;
}

/* Product Fields Container - Compact layout */
.product-fields-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 10px 0;
  padding: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(0, 212, 255, 0.1);
}

/* Interactive Selectors - Compact spacing */
/* Flexible and responsive selectors container - 3 column grid */
.selectors-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 12px;
  width: 100%;
  margin: 16px 0;
}

/* Enhanced flexible and responsive selector groups */
.selector-group {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  min-width: 0; /* Allow shrinking */
}

.selector-label {
  color: var(--text-color);
  font-weight: 600;
  font-size: 0.95rem;
  display: block;
  padding: 6px 10px;
  background: rgba(0, 212, 255, 0.08);
  border-radius: 6px;
  border-left: 3px solid var(--accent-color);
  flex-shrink: 0; /* Prevent label from shrinking */
  text-align: center;
}

.selector-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 4px;
  width: 100%;
  min-width: 0; /* Allow shrinking */
}

.selector-option {
  padding: 6px 12px;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 16px;
  color: var(--accent-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
  flex: 1 1 auto; /* Allow growing and shrinking */
  min-width: -webkit-fill-available; /* Samsung Internet support */
  min-width: fit-content;
  max-width: 100%;
  word-wrap: break-word;
  text-align: center;
  box-shadow: 0 1px 4px rgba(0, 212, 255, 0.1);
  overflow-wrap: break-word; /* Better text wrapping */
  -webkit-hyphens: auto; /* Safari iOS support */
  hyphens: auto; /* Enable hyphenation for long words */
}

.selector-option:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: var(--accent-color);
}

/* .selector-option.active {
  background: var(--accent-color);
  color: #000;
  border-color: var(--accent-color);
} */

.selector-option.active {
  background: #ffffff14;
  color: #ffffff;
  /* border-color: var(--accent-color); */
}

/* Quantity Selector */
.quantity-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 20px 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 25px;
  padding: 5px;
}

.quantity-btn {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.2rem;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background: rgba(0, 212, 255, 0.2);
  color: var(--accent-color);
}

.quantity-display {
  padding: 8px 16px;
  color: var(--text-color);
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

.add-to-cart-btn {
  background: var(--accent-color);
  color: #000;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.add-to-cart-btn:hover {
  background: #00a8cc;
  transform: translateY(-2px);
}

/* Enhanced Product Meta Info - Grid layout */
.product-meta {
  margin-top: 20px;
  /* padding: 16px; 
  background: rgba(0, 212, 255, 0.05);*/
  border-radius: 12px;
  /* border: 1px solid rgba(0, 212, 255, 0.1); */
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px 16px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  background: rgba(0, 212, 255, 0.08);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
  font-size: 0.85rem;
  line-height: 1.4;
}

.view-details-btn.disabled {
  /* background-color: #ccc; */
  color: #666;
  cursor: not-allowed;
}

.view-details-btn.disabled span {
  color: #999;
}

.view-details-btn.disabled .icon {
  opacity: 0.5;
}

.meta-label {
  color: var(--accent-color);
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.meta-value {
  color: var(--text-color);
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Additional content sections for left panel - Compact */
.product-specifications {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 10px;
  margin-top: 6px;
}

.section-title {
  color: var(--accent-color);
  font-size: 0.95rem;
  margin-bottom: 6px;
  font-weight: 600;
}

.specs-grid {
  display: grid;
  gap: 4px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  padding: 3px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-size: 0.85rem;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  color: #aaa;
  font-size: 0.9rem;
  text-transform: capitalize;
}

.spec-value {
  color: var(--text-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.reviews-summary {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
}

.rating-bar-container {
  margin-top: 10px;
}

.rating-overall {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rating-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--accent-color);
}

.rating-stars-small {
  transform: scale(0.8);
  transform-origin: left center;
}

/* Scrollbar styling for thumbnails */
.thumbnails-container::-webkit-scrollbar {
  height: 6px;
}

.thumbnails-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.thumbnails-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 3px;
}

/* Loading status text */
.loading-status-text {
  text-align: center;
  color: #888;
  font-size: 1rem;
  margin: 20px 0;
  font-weight: 500;
  animation: pulse-text 1.5s ease-in-out infinite;
}

@keyframes pulse-text {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.thumbnails-container::-webkit-scrollbar-thumb:hover {
  background: #00a8cc;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .modal-left-section {
    flex: 0 0 clamp(320px, 40%, 450px);
  }
}

@media (max-width: 1200px) {
  .modal-content {
    max-width: 95vw;
    width: 95vw;
  }

  .modal-left-section {
    flex: 0 0 clamp(300px, 42%, 400px);
  }
}

/* Tablet Modal Optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .modal-body {
    color: var(--text-color);
    line-height: 1.5;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 15px 20px;
    min-height: 0;
  }

  .modal-right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 0;
    overflow-y: auto;
    padding-left: 15px;
  }

  .modal-content {
    max-width: 90vw;
    width: 90vw;
    height: 90vh;
    max-height: 90vh;
  }

  .modal-left-section {
    flex: 0 0 clamp(280px, 45%, 384px);
  }

  .main-image-container {
    padding: 8px;
  }

  .pairing-recommendations {
    padding: 8px;
    height: auto;
  }

  .image-thumbnails {
    height: 80px;
  }

  .thumbnails-container {
    height: 64px;
  }

  .thumbnail-image {
    width: 60px;
    height: 60px;
  }

  .thumbnail-nav {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* Specific Mobile Device (384x832) */
@media (max-width: 384px) and (max-height: 832px) {
  .jmsclogo {
    height: 22px; /* Even smaller for very small devices */
    margin-right: 6px;
  }

  /* Landing Page Adjustments */
  .landing-title {
    font-size: 1.8rem;
    margin-bottom: 8px;
  }

  .landing-subtitle {
    font-size: 0.9rem;
  }

  .start-chat-btn {
    margin-top: 20px;
    padding: 10px 18px;
    font-size: 0.9rem;
  }

  .landing-footer {
    bottom: 20px;
    font-size: 0.8rem;
  }

  .footerLogo {
    height: 28px;
  }

  /* Main App Layout Adjustments */
  .app-header {
    padding: 8px 12px;
    height: 50px;
  }

  .logo {
    font-size: 1.3rem;
  }

  .fullscreen-btn {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .content-container {
    height: calc(100vh - 50px); /* Adjust for new header height */
    flex-direction: column; /* Stack panels vertically */
  }

  .left-panel {
    width: 100%;
    height: 60%; /* Allocate more space for chat/visualizer */
    padding: 10px;
    border-right: none;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2); /* Add bottom border */
    justify-content: flex-start; /* Align content to top */
    order: 2; /* Place left panel (chat) second */
  }

  .left-panel.split-view {
    width: 100%;
    height: 60%; /* Maintain height when split */
    border-right: none;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  }

  .right-panel {
    width: 100%;
    height: 40%; /* Allocate remaining space for products */
    padding: 10px;
    opacity: 1; /* Always show when active for this dimension */
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    order: 1; /* Place right panel (products) first */
  }

  .right-panel.show {
    width: 100%;
    height: 40%;
    padding: 10px;
    opacity: 1;
  }

  .visualizer-container {
    display: none !important; /* Hide the visualizer for this dimension */
    margin-top: 0 !important; /* Ensure no residual margin */
  }

  .main-circle {
    width: 180px;
    height: 180px;
    border-width: 2px;
    box-shadow: 0 0 20px #00d4ff, 0 0 40px rgba(0, 212, 255, 0.4),
      0 0 60px rgba(0, 212, 255, 0.2), inset 0 0 20px rgba(0, 212, 255, 0.1);
  }

  .outer-ring {
    width: 220px;
    height: 220px;
    top: -20px;
    left: -20px;
  }

  .audio-bars {
    height: 30px;
    gap: 3px;
  }

  .bar {
    width: 5px;
  }

  .listening-indicator {
    margin-top: 10px;
    font-size: 0.9rem;
  }

  .listening-icon {
    width: 10px;
    height: 10px;
  }

  .left-panel {
    /* ... existing styles ... */
    justify-content: flex-end; /* Push content to the bottom */
    padding-bottom: 0; /* Remove any bottom padding that might cause space */
  }

  .message-container {
    width: 98%; /* Take almost full width */
    max-width: none; /* Remove max-width constraint */
    padding: 8px;
    height: 100%; /* Make it fill the available space in left-panel */
    max-height: none; /* Remove max-height constraint */
    min-height: auto; /* Remove min-height constraint */
    margin-top: 0; /* Remove margin-top if it's causing space */
  }

  #messages {
    padding-bottom: 5px;
    gap: 8px;
  }

  #messageForm {
    padding: 5px 8px;
    min-height: 40px;
  }

  #message {
    padding: 8px 10px;
    font-size: 14px;
    max-height: 80px;
  }

  .mic-button,
  #sendButton {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }

  .right-panel {
    width: 100%;
    height: 40%; /* Allocate remaining space for products */
    padding: 0;
    opacity: 0;
    transition: height 0.5s ease, padding 0.5s ease, opacity 0.5s ease;
  }

  .right-panel.show {
    width: 100%;
    height: 40%;
    padding: 10px;
    opacity: 1;
  }

  .recommendations-panel h2 {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }

  .product-grid,
  #recommendedProducts {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    padding: 10px;
  }

  .product-card {
    flex-direction: column; /* Stack image and details vertically */
    min-height: auto;
  }

  .product-image-container {
    width: 100%;
    height: 150px; /* Fixed height for image */
    padding: 10px;
  }

  .product-details {
    width: 100%;
    padding: 15px;
    gap: 8px;
  }

  .product-title {
    font-size: 1rem;
    -webkit-line-clamp: 3; /* Allow more lines for title */
  }

  .product-price {
    font-size: 1.2rem;
  }

  .product-info-row {
    flex-direction: row; /* Keep info rows horizontal */
    flex-wrap: wrap;
    gap: 5px;
  }

  .product-size-info,
  .product-location-info {
    font-size: 0.75rem;
    padding: 3px 6px;
  }

  .view-details-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
    min-width: 100px;
  }

  .view-details-btn .icon {
    width: 18px;
    height: 18px;
  }

  /* Modal Adjustments */
  .modal-content {
    max-width: 98vw;
    width: 98vw;
    height: 98vh;
    max-height: 98vh;
    flex-direction: column;
  }

  .modal-header {
    padding: 10px 15px;
  }

  .modal-title {
    font-size: 1.4rem;
  }

  .close-btn {
    font-size: 20px;
    width: 30px;
    height: 30px;
  }

  .modal-body {
    flex-direction: column;
    gap: 10px;
    padding: 10px 15px;
  }

  .modal-left-section {
    flex: none;
    height: auto;
    min-height: 200px;
    max-height: 40vh;
    gap: 6px;
  }

  .product-image-gallery {
    height: 80%;
    min-height: 150px;
  }

  .main-image-container {
    padding: 5px;
  }

  .pairing-recommendations {
    height: 20%;
    min-height: 50px;
    padding: 5px 5px 5px 0;
  }

  .pairing-title {
    font-size: 1rem;
    margin-bottom: 4px;
  }

  .pairing-item {
    font-size: 0.75rem;
  }

  .image-thumbnails {
    height: 70px;
  }

  .thumbnails-container {
    height: 60px;
    padding: 5px;
    gap: 8px;
  }

  .thumbnail-image {
    width: 50px;
    height: 50px;
  }

  .thumbnail-nav {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .modal-right-section {
    flex: 1;
    padding-left: 0;
    gap: 6px;
  }

  .product-title-large {
    font-size: 1.4rem;
  }

  .product-price-large {
    font-size: 1.6rem;
  }

  .product-description-text {
    font-size: 0.85rem;
    max-height: 80px;
  }

  .selectors-container {
    grid-template-columns: repeat(2, 1fr); /* 2 columns for selectors */
    gap: 10px;
    margin: 10px 0;
  }

  .selector-label {
    font-size: 0.85rem;
    padding: 4px 8px;
  }

  .selector-option {
    padding: 5px 10px;
    font-size: 0.8rem;
  }

  .product-meta {
    gap: 8px;
    padding: 10px;
  }

  .meta-item {
    padding: 6px;
    font-size: 0.8rem;
  }

  .meta-label {
    font-size: 0.75rem;
  }

  .meta-value {
    font-size: 0.8rem;
  }

  .product-specifications {
    padding: 8px;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .spec-item {
    font-size: 0.8rem;
  }

  .spec-label,
  .spec-value {
    font-size: 0.8rem;
  }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
  .modal-left-section {
    min-height: 250px;
    max-height: 45vh;
  }

  .product-image-gallery {
    min-height: 180px;
  }

  .pairing-recommendations {
    min-height: 50px;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.3);
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.6);
}

/* Touch-friendly improvements for tablets */
@media (min-width: 768px) and (max-width: 1024px) {
  .fullscreen-btn {
    width: 44px;
    height: 44px;
    font-size: 1.3rem;
    min-width: 44px;
    min-height: 44px;
  }

  .mic-button,
  #sendButton {
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
  }

  .view-details-btn {
    padding: 12px 16px;
    min-height: 44px;
    font-size: 0.9rem;
  }

  .selector-option {
    padding: 10px 14px;
    min-height: 44px;
  }

  .thumbnail-nav {
    width: 44px;
    height: 44px;
    font-size: 16px;
  }

  /* Enhanced touch experience for image gallery */
  .main-image-container {
    position: relative;
  }

  .main-image-container::before {
    content: "← Swipe to navigate →";
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: rgba(255, 255, 255, 0.8);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.7rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
  }

  .main-image-container:hover::before {
    opacity: 1;
  }

  /* Larger touch targets for thumbnails */
  .thumbnail-image {
    width: 90px;
    height: 90px;
    margin: 5px;
  }

  .thumbnails-container {
    height: 100px;
    padding: 10px;
  }
}

/* Styles for products displayed inline within the chatbox (384x832) */
@media (max-width: 384px) and (max-height: 832px) {
  .product-card-inline {
    width: 95%; /* Adjust width to fit within chat bubble area */
    max-width: 300px; /* Max width for inline cards */
    margin: 10px auto; /* Center the card and add vertical spacing */
    flex-direction: column; /* Stack image and details vertically */
    align-self: center; /* Center within the messages div */
    background: linear-gradient(145deg, var(--card-bg), rgba(30, 30, 66, 0.9));
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 212, 255, 0.2);
  }

  .product-card-inline .product-image-container {
    width: 100%;
    height: 180px; /* Larger image area for inline cards */
    padding: 15px;
  }

  .product-card-inline .product-details {
    width: 100%;
    padding: 15px;
    gap: 10px;
  }

  .product-card-inline .product-title {
    font-size: 1.1rem;
    -webkit-line-clamp: 2; /* Limit title lines */
  }

  .product-card-inline .product-price {
    font-size: 1.3rem;
  }

  .product-card-inline .product-info-row {
    flex-direction: column; /* Stack info rows vertically for better readability */
    gap: 4px;
  }

  .product-card-inline .product-footer {
    justify-content: center; /* Center the button */
    padding-top: 10px;
  }

  .product-card-loading-inline {
    width: 95%;
    max-width: 300px;
    margin: 10px auto;
    align-self: center;
    border-radius: 16px;
  }

  .product-card-loading-inline .loading-image {
    height: 180px;
  }
}

/* ===================================================================
   Suggested Prompts Styling (Gemini-inspired)
   =================================================================== */

.suggested-prompts-container {
  margin-top: 15px;
  opacity: 1;
  transition: opacity 0.3s ease, max-height 0.3s ease;
  max-height: 200px;
  overflow: hidden;
}

.suggested-prompts-container.hidden {
  opacity: 0;
  max-height: 0;
  margin-top: 0;
}

.suggested-prompts-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
  align-items: flex-start;
}

.suggested-prompt-button {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  padding: 8px 16px;
  color: var(--text-color);
  font-size: 0.9rem;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.suggested-prompt-button:hover {
  background: rgba(0, 212, 255, 0.15);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);
}

.suggested-prompt-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 212, 255, 0.15);
}

/* Animation for prompt appearance */
.suggested-prompt-button {
  animation: fadeInUp 0.4s ease forwards;
  opacity: 0;
  transform: translateY(10px);
}

.suggested-prompt-button:nth-child(1) { animation-delay: 0.1s; }
.suggested-prompt-button:nth-child(2) { animation-delay: 0.15s; }
.suggested-prompt-button:nth-child(3) { animation-delay: 0.2s; }
.suggested-prompt-button:nth-child(4) { animation-delay: 0.25s; }
.suggested-prompt-button:nth-child(5) { animation-delay: 0.3s; }
.suggested-prompt-button:nth-child(6) { animation-delay: 0.35s; }
.suggested-prompt-button:nth-child(7) { animation-delay: 0.4s; }
.suggested-prompt-button:nth-child(8) { animation-delay: 0.45s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles for suggested prompts */
@media (max-width: 768px) {
  .suggested-prompts-grid {
    gap: 6px;
  }

  .suggested-prompt-button {
    font-size: 0.85rem;
    padding: 6px 12px;
    max-width: 240px;
  }
}

@media (max-width: 480px) {
  .suggested-prompts-container {
    margin-top: 12px;
  }

  .suggested-prompts-grid {
    gap: 5px;
  }

  .suggested-prompt-button {
    font-size: 0.8rem;
    padding: 5px 10px;
    max-width: 200px;
    border-radius: 16px;
  }

  /* Hide suggested prompts when keyboard is active on mobile */
  .message-container:focus-within .suggested-prompts-container {
    display: none;
  }
}
